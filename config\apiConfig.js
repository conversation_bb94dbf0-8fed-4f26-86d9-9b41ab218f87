
// --- 环境配置 ---
// 微信小程序的发布环境有 develop, trial, release
// 我们将其映射到两个配置环境：dev 和 prod
const envMap = {
  'develop': 'dev',  // 开发版
  'trial':   'trial',  // 体验版
  'release': 'prod'  // 正式版
};

// 获取当前运行环境
const accountInfo = wx.getAccountInfoSync();
const currentEnv = envMap[accountInfo.miniProgram.envVersion] || 'prod';


// --- 通用配置 (所有环境共享) ---
const commonConfig = {
  // 全局超时配置
  timeout: {
    previewImage: 10000, // 简历预览图片生成超时时间（毫秒）
    generatePDF: 10000,  // PDF生成超时时间（毫秒）
    default: 5000        // 普通API请求超时时间（毫秒）
  },

  // API 路径 (这些路径在不同环境下通常是固定的)
  generatePDFUrl:                 '/resumerender/export-pdf',
  exportJpegUrl:                  '/resumerender/export-jpeg',
  loginUrl:                       '/auth/login',
  userInfoUrl:                    '/auth/user',
  updateUserInfoUrl:              '/auth/user',
  feedbackUrl:                    '/feedback/submit',
  errorReportUrl:                 '/error/report',
  freeTemplatesUrl:               '/free-templates',
  resumeStylesUrl:                '/free-templates/styles',
  resumeStyleDetailUrl:           '/free-templates/styles',
  idPhotoHealthUrl:               '/idphoto/health',
  idPhotoGenerateUrl:             '/idphoto/generate',
  idPhotoGenerateTransparentUrl:  '/idphoto/generate_transparent',
  idPhotoGenerateAddColorUrl:     '/idphoto/generate_add_color',
  idPhotoSizesUrl:                '/idphoto/sizes',
  idPhotoColorsUrl:               '/idphoto/colors',

  wxAppName:                      '个人简历制作花花版',

  // 多商户配置
  merchant: {
    // 默认商户代码（如果未配置或获取失败时使用）
    defaultCode: 'default',

    // 商户代码验证规则
    validation: {
      minLength: 2,
      maxLength: 50,
      pattern: /^[a-zA-Z0-9_-]+$/  // 只允许字母、数字、下划线、连字符
    }
  },

  // 支付相关API
  membershipPlansUrl:             '/payment/membership-plans',
  createOrderUrl:                 '/payment/create-order',
  queryOrderUrl:                  '/payment/query-order',
  orderListUrl:                   '/payment/order-list',
  
  // 卡密系统API
  redeemCardUrl:                  '/card/redeem',
  cardInfoUrl:                    '/card/info',
  
  // 用户会员状态API
  membershipStatusUrl:            '/user/membership-status'
};


// --- 环境特定配置 ---
const environmentConfig = {
  // 开发环境配置
  dev: {
    // baseUrl:                'https://resume.gbw8848.cn', // 开发和测试服务器
    // idPhotoBaseUrl:         'https://resume.gbw8848.cn'
    baseUrl:                'http://*************:18080', // 开发和测试服务器
    idPhotoBaseUrl:         'http://*************:18080',

    // 开发环境商户配置
    merchant: {
      code: 'dev_merchant',  // 开发环境默认商户代码
      name: '开发测试商户'
    }
  },

  // 体验环境配置
  trial: {
    baseUrl:                'https://resume.gbw8848.cn',
    idPhotoBaseUrl:         'https://resume.gbw8848.cn',

    // 体验环境商户配置
    merchant: {
      code: 'trial_merchant',  // 体验环境默认商户代码
      name: '体验版商户'
    }
  },

  // 生产环境配置
  prod: {
    baseUrl:                'https://resume.gbw8848.cn', // 【请替换为你的生产环境API地址】
    idPhotoBaseUrl:         'https://resume.gbw8848.cn', // 【请替换为你的生产环境证件照API地址】

    // 生产环境商户配置
    merchant: {
      code: 'default',  // 生产环境默认商户代码
      name: '默认商户'
    }
  }
};


// --- 合并并导出配置 ---
// 使用当前环境的配置覆盖通用配置
const finalConfig = {
  ...commonConfig,
  ...environmentConfig[currentEnv],
  // 确保merchant配置正确合并
  merchant: {
    ...commonConfig.merchant,
    ...(environmentConfig[currentEnv].merchant || {})
  }
};

/**
 * 获取当前商户代码
 * @returns {string} 商户代码
 */
function getMerchantCode() {
  // 优先从环境配置获取
  if (finalConfig.merchant && finalConfig.merchant.code) {
    return finalConfig.merchant.code;
  }

  // 使用默认商户代码
  return finalConfig.merchant.defaultCode || 'default';
}

// /**
//  * 验证商户代码格式
//  * @param {string} code 商户代码
//  * @returns {boolean} 是否有效
//  */
// function validateMerchantCode(code) {
//   if (!code || typeof code !== 'string') {
//     return false;
//   }

//   const { minLength, maxLength, pattern } = finalConfig.merchant.validation;

//   return code.length >= minLength &&
//          code.length <= maxLength &&
//          pattern.test(code);
// }

// /**
//  * 设置商户代码（用于动态切换商户）
//  * @param {string} code 商户代码
//  * @returns {boolean} 设置是否成功
//  */
// function setMerchantCode(code) {
//   if (!validateMerchantCode(code)) {
//     console.error('无效的商户代码:', code);
//     return false;
//   }

//   // 更新当前配置中的商户代码
//   if (!finalConfig.merchant) {
//     finalConfig.merchant = {};
//   }
//   finalConfig.merchant.code = code;

//   console.log('商户代码已更新为:', code);
//   return true;
// }

console.log(`当前环境: ${currentEnv}`);
console.log(`当前商户: ${getMerchantCode()}`);
// console.log('最终API配置:', finalConfig);

// 导出配置和工具函数
module.exports = {
  ...finalConfig,
  getMerchantCode,
  // validateMerchantCode,
  // setMerchantCode
};
