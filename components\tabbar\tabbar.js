Component({
  properties: {
    currentTab: {
      type: String,
      value: 'home'
    }
  },

  methods: {
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      
      if (tab === 'home') {
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else if (tab === 'user') {
        wx.navigateTo({
          url: '/pages/user/center/center'
        });
      }
      
      this.triggerEvent('tabchange', { tab });
    }
  }
});