const apiConfig = require('../../config/apiConfig.js');
const request = require('../request.js');

/**
 * 支付相关API
 */
class PaymentApi {
  
  /**
   * 获取会员套餐列表
   */
  async getMembershipPlans() {
    try {
      const response = await request.get(apiConfig.membershipPlansUrl);
      return response;
    } catch (error) {
      console.error('获取会员套餐失败:', error);
      return { success: false, message: '获取套餐信息失败' };
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   */
  async createOrder(orderData) {
    try {
      const response = await request.post(apiConfig.createOrderUrl, orderData);
      return response;
    } catch (error) {
      console.error('创建订单失败:', error);
      return { success: false, message: '创建订单失败' };
    }
  }

  /**
   * 查询订单状态
   * @param {string} orderId 订单ID
   */
  async queryOrder(orderId) {
    try {
      const response = await request.get(`${apiConfig.queryOrderUrl}/${orderId}`);
      return response;
    } catch (error) {
      console.error('查询订单失败:', error);
      return { success: false, message: '查询订单失败' };
    }
  }

  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   */
  async getOrderList(params = {}) {
    try {
      const response = await request.get(apiConfig.orderListUrl, params);
      return response;
    } catch (error) {
      console.error('获取订单列表失败:', error);
      return { success: false, message: '获取订单列表失败' };
    }
  }

  /**
   * 发起微信支付
   * @param {Object} paymentData 支付数据
   */
  async requestWxPayment(paymentData) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentData.timeStamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: (res) => {
          console.log('支付成功:', res);
          resolve({ success: true, data: res });
        },
        fail: (err) => {
          console.error('支付失败:', err);
          if (err.errMsg === 'requestPayment:fail cancel') {
            resolve({ success: false, message: '用户取消支付', cancelled: true });
          } else {
            resolve({ success: false, message: '支付失败', error: err });
          }
        }
      });
    });
  }

  /**
   * 获取用户会员状态
   */
  async getMembershipStatus() {
    try {
      const response = await request.get(apiConfig.membershipStatusUrl);
      return response;
    } catch (error) {
      console.error('获取会员状态失败:', error);
      return { success: false, message: '获取会员状态失败' };
    }
  }
}

module.exports = new PaymentApi();